{"name": "ai-web-dev-platform", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "mastra:dev": "<PERSON>ra dev", "mastra:build": "mastra build", "test": "playwright test", "test:ui": "playwright test --ui"}, "dependencies": {"@vibe-kit/dagger": "^0.0.2", "clsx": "^2.1.1", "lucide-react": "^0.535.0", "next": "15.4.5", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.5", "tailwindcss": "^4", "typescript": "^5"}, "trustedDependencies": ["@tailwindcss/oxide", "unrs-resolver"]}