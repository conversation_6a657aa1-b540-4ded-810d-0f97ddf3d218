export interface WebsiteRequest {
  type: 'text' | 'url' | 'image';
  content: string;
  options?: {
    framework?: 'react' | 'nextjs' | 'vue' | 'svelte';
    styling?: 'tailwind' | 'css' | 'styled-components';
    theme?: 'light' | 'dark' | 'auto';
    responsive?: boolean;
    animations?: boolean;
  };
}

export interface GeneratedWebsite {
  id: string;
  name: string;
  description: string;
  files: GeneratedFile[];
  preview?: string;
  deployUrl?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface GeneratedFile {
  path: string;
  content: string;
  type: 'component' | 'page' | 'style' | 'config' | 'asset';
  language: 'typescript' | 'javascript' | 'css' | 'html' | 'json';
}

export interface DesignPattern {
  name: string;
  description: string;
  cssClasses: string[];
  properties: Record<string, any>;
  examples: string[];
}

export interface WorkflowStep {
  id: string;
  name: string;
  description: string;
  status: 'pending' | 'running' | 'completed' | 'error';
  output?: string;
  error?: string;
  timestamp: Date;
}

export interface PlatformState {
  currentProject?: GeneratedWebsite;
  workflowSteps: WorkflowStep[];
  isGenerating: boolean;
  previewMode: 'desktop' | 'tablet' | 'mobile';
  sidebarCollapsed: boolean;
}

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  metadata?: {
    type?: 'workflow' | 'error' | 'success';
    files?: string[];
    actions?: string[];
  };
}

export interface ExportOptions {
  format: 'zip' | 'github' | 'codesandbox';
  includeAssets: boolean;
  minify: boolean;
  deployment?: {
    platform: 'vercel' | 'netlify' | 'github-pages';
    config?: Record<string, any>;
  };
}

// Configuration Types
export interface SandboxConfig {
  provider: 'e2b' | 'dagger';
  e2b?: {
    apiKey: string;
    templateId: string;
  };
  dagger?: {
    githubToken?: string;
    preferRegistryImages?: boolean;
    dockerHubUser?: string;
    privateRegistry?: string;
  };
}

export interface AIModelConfig {
  provider: 'openai' | 'anthropic' | 'google' | 'openai-compatible';
  model: string;
  apiKey: string;
  baseUrl?: string; // For OpenAI-compatible providers
  customName?: string; // For display purposes
}

export interface PlatformConfig {
  sandbox: SandboxConfig;
  aiModel: AIModelConfig;
  preferences: {
    theme: 'light' | 'dark' | 'auto';
    defaultFramework: 'react' | 'nextjs' | 'vue' | 'svelte';
    defaultStyling: 'tailwind' | 'css' | 'styled-components';
    autoSave: boolean;
    showWorkflowDetails: boolean;
  };
}

export interface ConfigValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export interface ProviderCapabilities {
  textGeneration: boolean;
  codeGeneration: boolean;
  imageAnalysis: boolean;
  functionCalling: boolean;
  streaming: boolean;
  maxTokens: number;
  supportedLanguages: string[];
}

export interface ModelInfo {
  id: string;
  name: string;
  description: string;
  capabilities: ProviderCapabilities;
  pricing?: {
    input: number; // per 1K tokens
    output: number; // per 1K tokens
  };
}
