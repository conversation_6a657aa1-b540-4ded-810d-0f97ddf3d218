import { ModelInfo, ProviderCapabilities } from '@/src/types';

export const MODEL_PROVIDERS = {
  openai: {
    name: 'OpenAI',
    description: 'Advanced language models from OpenAI',
    requiresApiKey: true,
    website: 'https://platform.openai.com/',
  },
  anthropic: {
    name: 'Anthropic',
    description: 'Claude models from Anthropic',
    requiresApiKey: true,
    website: 'https://console.anthropic.com/',
  },
  google: {
    name: 'Google',
    description: 'Gemini models from Google',
    requiresApiKey: true,
    website: 'https://ai.google.dev/',
  },
  'openai-compatible': {
    name: 'OpenAI Compatible',
    description: 'Local models or other OpenAI-compatible APIs',
    requiresApiKey: true,
    website: 'https://ollama.ai/',
  },
} as const;

const highCapabilities: ProviderCapabilities = {
  textGeneration: true,
  codeGeneration: true,
  imageAnalysis: true,
  functionCalling: true,
  streaming: true,
  maxTokens: 128000,
  supportedLanguages: ['typescript', 'javascript', 'python', 'html', 'css', 'json', 'markdown'],
};

const mediumCapabilities: ProviderCapabilities = {
  textGeneration: true,
  codeGeneration: true,
  imageAnalysis: false,
  functionCalling: true,
  streaming: true,
  maxTokens: 32000,
  supportedLanguages: ['typescript', 'javascript', 'python', 'html', 'css', 'json', 'markdown'],
};

const basicCapabilities: ProviderCapabilities = {
  textGeneration: true,
  codeGeneration: true,
  imageAnalysis: false,
  functionCalling: false,
  streaming: true,
  maxTokens: 16000,
  supportedLanguages: ['typescript', 'javascript', 'html', 'css', 'json'],
};

export const AVAILABLE_MODELS: Record<string, ModelInfo[]> = {
  openai: [
    {
      id: 'gpt-4o',
      name: 'GPT-4o',
      description: 'Most advanced multimodal model with vision capabilities',
      capabilities: highCapabilities,
      pricing: { input: 2.5, output: 10.0 },
    },
    {
      id: 'gpt-4o-mini',
      name: 'GPT-4o Mini',
      description: 'Fast and efficient model for most tasks',
      capabilities: mediumCapabilities,
      pricing: { input: 0.15, output: 0.6 },
    },
    {
      id: 'gpt-4-turbo',
      name: 'GPT-4 Turbo',
      description: 'High-performance model with large context window',
      capabilities: { ...highCapabilities, maxTokens: 128000 },
      pricing: { input: 10.0, output: 30.0 },
    },
    {
      id: 'gpt-3.5-turbo',
      name: 'GPT-3.5 Turbo',
      description: 'Fast and cost-effective for simpler tasks',
      capabilities: basicCapabilities,
      pricing: { input: 0.5, output: 1.5 },
    },
  ],
  anthropic: [
    {
      id: 'claude-3-5-sonnet-20241022',
      name: 'Claude 3.5 Sonnet',
      description: 'Most intelligent model with excellent reasoning',
      capabilities: highCapabilities,
      pricing: { input: 3.0, output: 15.0 },
    },
    {
      id: 'claude-3-5-haiku-20241022',
      name: 'Claude 3.5 Haiku',
      description: 'Fast and efficient for everyday tasks',
      capabilities: mediumCapabilities,
      pricing: { input: 0.8, output: 4.0 },
    },
    {
      id: 'claude-3-opus-20240229',
      name: 'Claude 3 Opus',
      description: 'Most powerful model for complex reasoning',
      capabilities: { ...highCapabilities, maxTokens: 200000 },
      pricing: { input: 15.0, output: 75.0 },
    },
  ],
  google: [
    {
      id: 'gemini-1.5-pro',
      name: 'Gemini 1.5 Pro',
      description: 'Advanced multimodal model with large context',
      capabilities: { ...highCapabilities, maxTokens: 1000000 },
      pricing: { input: 1.25, output: 5.0 },
    },
    {
      id: 'gemini-1.5-flash',
      name: 'Gemini 1.5 Flash',
      description: 'Fast and efficient multimodal model',
      capabilities: mediumCapabilities,
      pricing: { input: 0.075, output: 0.3 },
    },
    {
      id: 'gemini-1.0-pro',
      name: 'Gemini 1.0 Pro',
      description: 'Reliable model for text generation',
      capabilities: basicCapabilities,
      pricing: { input: 0.5, output: 1.5 },
    },
  ],
  'openai-compatible': [
    {
      id: 'llama-3.2-90b',
      name: 'Llama 3.2 90B',
      description: 'Large open-source model (requires local setup)',
      capabilities: mediumCapabilities,
    },
    {
      id: 'llama-3.2-11b',
      name: 'Llama 3.2 11B',
      description: 'Medium open-source model (requires local setup)',
      capabilities: basicCapabilities,
    },
    {
      id: 'codellama-34b',
      name: 'CodeLlama 34B',
      description: 'Code-specialized model (requires local setup)',
      capabilities: { ...basicCapabilities, codeGeneration: true },
    },
    {
      id: 'custom',
      name: 'Custom Model',
      description: 'Your own OpenAI-compatible model',
      capabilities: basicCapabilities,
    },
  ],
};

export function getModelsByProvider(provider: string): ModelInfo[] {
  return AVAILABLE_MODELS[provider] || [];
}

export function getModelInfo(provider: string, modelId: string): ModelInfo | undefined {
  const models = getModelsByProvider(provider);
  return models.find(model => model.id === modelId);
}

export function validateApiKey(provider: string, apiKey: string): boolean {
  if (!apiKey || apiKey.trim().length === 0) {
    return false;
  }

  switch (provider) {
    case 'openai':
      return apiKey.startsWith('sk-') && apiKey.length > 20;
    case 'anthropic':
      return apiKey.startsWith('sk-ant-') && apiKey.length > 20;
    case 'google':
      return apiKey.length > 10; // Google API keys vary in format
    case 'openai-compatible':
      return apiKey.length > 0; // Custom providers can have any format
    default:
      return false;
  }
}

export function getProviderSetupInstructions(provider: string): string[] {
  switch (provider) {
    case 'openai':
      return [
        '1. Visit https://platform.openai.com/',
        '2. Sign up or log in to your account',
        '3. Navigate to API Keys section',
        '4. Create a new API key',
        '5. Copy the key (starts with sk-)',
      ];
    case 'anthropic':
      return [
        '1. Visit https://console.anthropic.com/',
        '2. Sign up or log in to your account',
        '3. Go to API Keys section',
        '4. Generate a new API key',
        '5. Copy the key (starts with sk-ant-)',
      ];
    case 'google':
      return [
        '1. Visit https://ai.google.dev/',
        '2. Sign in with your Google account',
        '3. Create a new project or select existing',
        '4. Enable the Generative AI API',
        '5. Create credentials and copy the API key',
      ];
    case 'openai-compatible':
      return [
        '1. Set up your local model server (e.g., Ollama)',
        '2. Ensure the server is running and accessible',
        '3. Note the base URL (e.g., http://localhost:11434/v1)',
        '4. Create an API key if required by your setup',
        '5. Test the connection with a simple request',
      ];
    default:
      return ['Please refer to the provider documentation for setup instructions.'];
  }
}
