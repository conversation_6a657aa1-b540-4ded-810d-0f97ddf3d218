"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { PlatformConfig, SandboxConfig, AIModelConfig, ConfigValidationResult } from '@/src/types';

interface ConfigContextType {
  config: PlatformConfig;
  updateSandboxConfig: (sandbox: SandboxConfig) => void;
  updateAIModelConfig: (aiModel: AIModelConfig) => void;
  updatePreferences: (preferences: Partial<PlatformConfig['preferences']>) => void;
  validateConfig: () => ConfigValidationResult;
  resetConfig: () => void;
  exportConfig: () => string;
  importConfig: (configJson: string) => boolean;
  isConfigValid: boolean;
}

const defaultConfig: PlatformConfig = {
  sandbox: {
    provider: 'dagger', // Default to local Dagger for better UX
    dagger: {
      preferRegistryImages: true,
    }
  },
  aiModel: {
    provider: 'openai',
    model: 'gpt-4o-mini',
    apiKey: '',
  },
  preferences: {
    theme: 'dark',
    defaultFramework: 'nextjs',
    defaultStyling: 'tailwind',
    autoSave: true,
    showWorkflowDetails: true,
  }
};

const ConfigContext = createContext<ConfigContextType | undefined>(undefined);

export function ConfigProvider({ children }: { children: ReactNode }) {
  const [config, setConfig] = useState<PlatformConfig>(defaultConfig);
  const [isConfigValid, setIsConfigValid] = useState(false);

  // Load config from localStorage on mount
  useEffect(() => {
    const savedConfig = localStorage.getItem('ai-web-dev-platform-config');
    if (savedConfig) {
      try {
        const parsedConfig = JSON.parse(savedConfig);
        setConfig({ ...defaultConfig, ...parsedConfig });
      } catch (error) {
        console.error('Failed to parse saved config:', error);
      }
    }
  }, []);

  // Save config to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('ai-web-dev-platform-config', JSON.stringify(config));
    const validation = validateConfig();
    setIsConfigValid(validation.isValid);
  }, [config]);

  const updateSandboxConfig = (sandbox: SandboxConfig) => {
    setConfig(prev => ({ ...prev, sandbox }));
  };

  const updateAIModelConfig = (aiModel: AIModelConfig) => {
    setConfig(prev => ({ ...prev, aiModel }));
  };

  const updatePreferences = (preferences: Partial<PlatformConfig['preferences']>) => {
    setConfig(prev => ({
      ...prev,
      preferences: { ...prev.preferences, ...preferences }
    }));
  };

  const validateConfig = (): ConfigValidationResult => {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Validate AI Model Config
    if (!config.aiModel.apiKey) {
      errors.push('AI model API key is required');
    }

    if (!config.aiModel.model) {
      errors.push('AI model selection is required');
    }

    // Validate Sandbox Config
    if (config.sandbox.provider === 'e2b') {
      if (!config.sandbox.e2b?.apiKey) {
        errors.push('E2B API key is required when using E2B sandbox');
      }
      if (!config.sandbox.e2b?.templateId) {
        warnings.push('E2B template ID not specified, using default');
      }
    }

    // Validate OpenAI-compatible providers
    if (config.aiModel.provider === 'openai-compatible') {
      if (!config.aiModel.baseUrl) {
        errors.push('Base URL is required for OpenAI-compatible providers');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  };

  const resetConfig = () => {
    setConfig(defaultConfig);
    localStorage.removeItem('ai-web-dev-platform-config');
  };

  const exportConfig = (): string => {
    return JSON.stringify(config, null, 2);
  };

  const importConfig = (configJson: string): boolean => {
    try {
      const importedConfig = JSON.parse(configJson);
      // Validate the imported config structure
      if (importedConfig.sandbox && importedConfig.aiModel && importedConfig.preferences) {
        setConfig({ ...defaultConfig, ...importedConfig });
        return true;
      }
      return false;
    } catch (error) {
      console.error('Failed to import config:', error);
      return false;
    }
  };

  const value: ConfigContextType = {
    config,
    updateSandboxConfig,
    updateAIModelConfig,
    updatePreferences,
    validateConfig,
    resetConfig,
    exportConfig,
    importConfig,
    isConfigValid
  };

  return (
    <ConfigContext.Provider value={value}>
      {children}
    </ConfigContext.Provider>
  );
}

export function useConfig() {
  const context = useContext(ConfigContext);
  if (context === undefined) {
    throw new Error('useConfig must be used within a ConfigProvider');
  }
  return context;
}
