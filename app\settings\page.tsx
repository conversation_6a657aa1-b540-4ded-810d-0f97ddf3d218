"use client";

import { useState } from 'react';
import { ArrowLeft, Save, RotateCcw, Download, Upload, TestTube, CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import Link from 'next/link';
import { useConfig } from '@/src/lib/config-context';
import { SandboxConfig, AIModelConfig } from '@/src/types';
import { MODEL_PROVIDERS, getModelsByProvider, validateApiKey, getProviderSetupInstructions } from '@/src/lib/model-definitions';
import { testSandboxConnection, testAIModelConnection } from '@/src/lib/vibekit';
import { CONFIGURATION_PRESETS, applyPreset } from '@/src/lib/configuration-presets';

export default function SettingsPage() {
  const { config, updateSandboxConfig, updateAIModelConfig, updatePreferences, validateConfig, resetConfig, exportConfig, importConfig } = useConfig();
  const [activeTab, setActiveTab] = useState<'presets' | 'sandbox' | 'ai-model' | 'preferences'>('presets');
  const [testResults, setTestResults] = useState<Record<string, 'testing' | 'success' | 'error' | null>>({});
  const [showApiKeySetup, setShowApiKeySetup] = useState<string | null>(null);

  const handleSandboxConfigChange = (updates: Partial<SandboxConfig>) => {
    updateSandboxConfig({ ...config.sandbox, ...updates });
  };

  const handleAIModelConfigChange = (updates: Partial<AIModelConfig>) => {
    updateAIModelConfig({ ...config.aiModel, ...updates });
  };

  const testConnection = async (type: 'sandbox' | 'ai-model') => {
    setTestResults(prev => ({ ...prev, [type]: 'testing' }));
    
    try {
      let success = false;
      if (type === 'sandbox') {
        success = await testSandboxConnection(config.sandbox);
      } else {
        success = await testAIModelConnection(config.aiModel);
      }
      
      setTestResults(prev => ({ ...prev, [type]: success ? 'success' : 'error' }));
    } catch (error) {
      setTestResults(prev => ({ ...prev, [type]: 'error' }));
    }
  };

  const handleExportConfig = () => {
    const configJson = exportConfig();
    const blob = new Blob([configJson], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'ai-web-dev-platform-config.json';
    a.click();
    URL.revokeObjectURL(url);
  };

  const handleImportConfig = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target?.result as string;
        const success = importConfig(content);
        if (!success) {
          alert('Failed to import configuration. Please check the file format.');
        }
      };
      reader.readAsText(file);
    }
  };

  const validation = validateConfig();

  return (
    <div className="min-h-screen bg-gray-950 text-gray-50">
      {/* Header */}
      <div className="border-b border-gray-800 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Link href="/" className="p-2 hover:bg-gray-800 rounded transition-colors">
              <ArrowLeft className="w-5 h-5" />
            </Link>
            <h1 className="text-xl font-semibold">Platform Settings</h1>
          </div>
          
          <div className="flex items-center gap-2">
            <button
              onClick={handleExportConfig}
              className="flex items-center gap-2 px-3 py-2 bg-gray-800 hover:bg-gray-700 rounded transition-colors"
            >
              <Download className="w-4 h-4" />
              Export
            </button>
            
            <label className="flex items-center gap-2 px-3 py-2 bg-gray-800 hover:bg-gray-700 rounded transition-colors cursor-pointer">
              <Upload className="w-4 h-4" />
              Import
              <input
                type="file"
                accept=".json"
                onChange={handleImportConfig}
                className="hidden"
              />
            </label>
            
            <button
              onClick={resetConfig}
              className="flex items-center gap-2 px-3 py-2 bg-red-600 hover:bg-red-700 rounded transition-colors"
            >
              <RotateCcw className="w-4 h-4" />
              Reset
            </button>
          </div>
        </div>
      </div>

      {/* Validation Status */}
      {!validation.isValid && (
        <div className="bg-red-900/20 border-b border-red-800 p-4">
          <div className="flex items-center gap-2 text-red-400">
            <AlertCircle className="w-5 h-5" />
            <span className="font-medium">Configuration Issues:</span>
          </div>
          <ul className="mt-2 space-y-1 text-sm text-red-300">
            {validation.errors.map((error, index) => (
              <li key={index}>• {error}</li>
            ))}
          </ul>
        </div>
      )}

      <div className="flex">
        {/* Sidebar */}
        <div className="w-64 border-r border-gray-800 p-4">
          <nav className="space-y-2">
            <button
              onClick={() => setActiveTab('presets')}
              className={`w-full text-left px-3 py-2 rounded transition-colors ${
                activeTab === 'presets' ? 'bg-blue-600 text-white' : 'hover:bg-gray-800'
              }`}
            >
              Quick Setup
            </button>
            <button
              onClick={() => setActiveTab('sandbox')}
              className={`w-full text-left px-3 py-2 rounded transition-colors ${
                activeTab === 'sandbox' ? 'bg-blue-600 text-white' : 'hover:bg-gray-800'
              }`}
            >
              Sandbox Environment
            </button>
            <button
              onClick={() => setActiveTab('ai-model')}
              className={`w-full text-left px-3 py-2 rounded transition-colors ${
                activeTab === 'ai-model' ? 'bg-blue-600 text-white' : 'hover:bg-gray-800'
              }`}
            >
              AI Model Provider
            </button>
            <button
              onClick={() => setActiveTab('preferences')}
              className={`w-full text-left px-3 py-2 rounded transition-colors ${
                activeTab === 'preferences' ? 'bg-blue-600 text-white' : 'hover:bg-gray-800'
              }`}
            >
              Preferences
            </button>
          </nav>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-6">
          {activeTab === 'presets' && (
            <div className="space-y-6">
              <div>
                <h2 className="text-lg font-semibold mb-4">Quick Setup Presets</h2>
                <p className="text-gray-400 mb-6">
                  Choose a pre-configured setup that matches your needs. You can customize any settings afterwards.
                </p>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                {CONFIGURATION_PRESETS.map((preset) => (
                  <div key={preset.id} className="border border-gray-700 rounded-lg p-6 hover:border-gray-600 transition-colors">
                    <div className="flex items-start gap-4 mb-4">
                      <div className="text-2xl">{preset.icon}</div>
                      <div className="flex-1">
                        <h3 className="font-semibold text-lg">{preset.name}</h3>
                        <p className="text-sm text-gray-400 mt-1">{preset.description}</p>
                      </div>
                    </div>

                    <div className="space-y-3 mb-4">
                      <div>
                        <h4 className="text-sm font-medium text-green-400 mb-1">Pros:</h4>
                        <ul className="text-xs text-gray-300 space-y-1">
                          {preset.pros.slice(0, 3).map((pro, index) => (
                            <li key={index}>• {pro}</li>
                          ))}
                        </ul>
                      </div>

                      <div>
                        <h4 className="text-sm font-medium text-yellow-400 mb-1">Requirements:</h4>
                        <ul className="text-xs text-gray-300 space-y-1">
                          {preset.requirements.slice(0, 2).map((req, index) => (
                            <li key={index}>• {req}</li>
                          ))}
                        </ul>
                      </div>
                    </div>

                    <button
                      onClick={() => {
                        const newConfig = applyPreset(preset.id, config);
                        updateSandboxConfig(newConfig.sandbox);
                        updateAIModelConfig(newConfig.aiModel);
                        updatePreferences(newConfig.preferences);
                      }}
                      className="w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded transition-colors text-sm"
                    >
                      Apply {preset.name}
                    </button>
                  </div>
                ))}
              </div>

              <div className="p-4 bg-gray-900 rounded-lg">
                <h4 className="font-medium mb-2">Need Help Choosing?</h4>
                <div className="text-sm text-gray-300 space-y-2">
                  <p><strong>New to AI development?</strong> Start with "Budget Friendly" or "Hybrid Balanced"</p>
                  <p><strong>Want maximum performance?</strong> Choose "Cloud Premium" or "Enterprise Ready"</p>
                  <p><strong>Privacy-focused or offline?</strong> Go with "Local & Free"</p>
                  <p><strong>Cost-conscious?</strong> Try "Budget Friendly" first</p>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'sandbox' && (
            <div className="space-y-6">
              <div>
                <h2 className="text-lg font-semibold mb-4">Sandbox Environment</h2>
                <p className="text-gray-400 mb-6">
                  Choose how code generation and execution will be handled. Local execution is faster but requires Docker, 
                  while cloud execution works anywhere but requires an API key.
                </p>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Sandbox Provider</label>
                  <div className="space-y-3">
                    <label className="flex items-center gap-3 p-4 border border-gray-700 rounded-lg cursor-pointer hover:border-gray-600">
                      <input
                        type="radio"
                        name="sandbox-provider"
                        value="dagger"
                        checked={config.sandbox.provider === 'dagger'}
                        onChange={() => handleSandboxConfigChange({ provider: 'dagger' })}
                        className="text-blue-600"
                      />
                      <div>
                        <div className="font-medium">Dagger (Local)</div>
                        <div className="text-sm text-gray-400">
                          Fast local execution using Docker. No API key required.
                        </div>
                      </div>
                    </label>
                    
                    <label className="flex items-center gap-3 p-4 border border-gray-700 rounded-lg cursor-pointer hover:border-gray-600">
                      <input
                        type="radio"
                        name="sandbox-provider"
                        value="e2b"
                        checked={config.sandbox.provider === 'e2b'}
                        onChange={() => handleSandboxConfigChange({ provider: 'e2b' })}
                        className="text-blue-600"
                      />
                      <div>
                        <div className="font-medium">E2B (Cloud)</div>
                        <div className="text-sm text-gray-400">
                          Cloud-based execution. Requires API key but works anywhere.
                        </div>
                      </div>
                    </label>
                  </div>
                </div>

                {config.sandbox.provider === 'e2b' && (
                  <div className="space-y-4 p-4 bg-gray-900 rounded-lg">
                    <div>
                      <label className="block text-sm font-medium mb-2">E2B API Key</label>
                      <input
                        type="password"
                        value={config.sandbox.e2b?.apiKey || ''}
                        onChange={(e) => handleSandboxConfigChange({
                          e2b: { ...config.sandbox.e2b, apiKey: e.target.value }
                        })}
                        placeholder="Enter your E2B API key"
                        className="w-full p-3 bg-gray-800 border border-gray-700 rounded focus:border-blue-500 focus:outline-none"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium mb-2">Template ID (Optional)</label>
                      <input
                        type="text"
                        value={config.sandbox.e2b?.templateId || ''}
                        onChange={(e) => handleSandboxConfigChange({
                          e2b: { ...config.sandbox.e2b, templateId: e.target.value }
                        })}
                        placeholder="vibekit-claude"
                        className="w-full p-3 bg-gray-800 border border-gray-700 rounded focus:border-blue-500 focus:outline-none"
                      />
                    </div>
                  </div>
                )}

                {config.sandbox.provider === 'dagger' && (
                  <div className="space-y-4 p-4 bg-gray-900 rounded-lg">
                    <div>
                      <label className="block text-sm font-medium mb-2">GitHub Token (Optional)</label>
                      <input
                        type="password"
                        value={config.sandbox.dagger?.githubToken || ''}
                        onChange={(e) => handleSandboxConfigChange({
                          dagger: { ...config.sandbox.dagger, githubToken: e.target.value }
                        })}
                        placeholder="For Git operations and PR creation"
                        className="w-full p-3 bg-gray-800 border border-gray-700 rounded focus:border-blue-500 focus:outline-none"
                      />
                    </div>
                    
                    <div className="flex items-center gap-3">
                      <input
                        type="checkbox"
                        id="prefer-registry-images"
                        checked={config.sandbox.dagger?.preferRegistryImages ?? true}
                        onChange={(e) => handleSandboxConfigChange({
                          dagger: { ...config.sandbox.dagger, preferRegistryImages: e.target.checked }
                        })}
                        className="text-blue-600"
                      />
                      <label htmlFor="prefer-registry-images" className="text-sm">
                        Use optimized registry images for faster startup
                      </label>
                    </div>
                  </div>
                )}

                <div className="flex items-center gap-3">
                  <button
                    onClick={() => testConnection('sandbox')}
                    disabled={testResults.sandbox === 'testing'}
                    className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 rounded transition-colors"
                  >
                    <TestTube className="w-4 h-4" />
                    {testResults.sandbox === 'testing' ? 'Testing...' : 'Test Connection'}
                  </button>
                  
                  {testResults.sandbox === 'success' && (
                    <div className="flex items-center gap-2 text-green-400">
                      <CheckCircle className="w-4 h-4" />
                      <span className="text-sm">Connection successful</span>
                    </div>
                  )}
                  
                  {testResults.sandbox === 'error' && (
                    <div className="flex items-center gap-2 text-red-400">
                      <XCircle className="w-4 h-4" />
                      <span className="text-sm">Connection failed</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'ai-model' && (
            <div className="space-y-6">
              <div>
                <h2 className="text-lg font-semibold mb-4">AI Model Provider</h2>
                <p className="text-gray-400 mb-6">
                  Configure which AI model to use for code generation and analysis. Different providers offer different capabilities and pricing.
                </p>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Provider</label>
                  <select
                    value={config.aiModel.provider}
                    onChange={(e) => handleAIModelConfigChange({ 
                      provider: e.target.value as AIModelConfig['provider'],
                      model: getModelsByProvider(e.target.value)[0]?.id || ''
                    })}
                    className="w-full p-3 bg-gray-800 border border-gray-700 rounded focus:border-blue-500 focus:outline-none"
                  >
                    {Object.entries(MODEL_PROVIDERS).map(([key, provider]) => (
                      <option key={key} value={key}>
                        {provider.name} - {provider.description}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Model</label>
                  <select
                    value={config.aiModel.model}
                    onChange={(e) => handleAIModelConfigChange({ model: e.target.value })}
                    className="w-full p-3 bg-gray-800 border border-gray-700 rounded focus:border-blue-500 focus:outline-none"
                  >
                    {getModelsByProvider(config.aiModel.provider).map((model) => (
                      <option key={model.id} value={model.id}>
                        {model.name} - {model.description}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <div className="flex items-center justify-between mb-2">
                    <label className="block text-sm font-medium">API Key</label>
                    <button
                      onClick={() => setShowApiKeySetup(showApiKeySetup === config.aiModel.provider ? null : config.aiModel.provider)}
                      className="text-xs text-blue-400 hover:text-blue-300"
                    >
                      Setup Instructions
                    </button>
                  </div>
                  <input
                    type="password"
                    value={config.aiModel.apiKey}
                    onChange={(e) => handleAIModelConfigChange({ apiKey: e.target.value })}
                    placeholder={`Enter your ${MODEL_PROVIDERS[config.aiModel.provider]?.name} API key`}
                    className="w-full p-3 bg-gray-800 border border-gray-700 rounded focus:border-blue-500 focus:outline-none"
                  />
                  {!validateApiKey(config.aiModel.provider, config.aiModel.apiKey) && config.aiModel.apiKey && (
                    <p className="text-sm text-red-400 mt-1">Invalid API key format</p>
                  )}
                </div>

                {config.aiModel.provider === 'openai-compatible' && (
                  <div>
                    <label className="block text-sm font-medium mb-2">Base URL</label>
                    <input
                      type="url"
                      value={config.aiModel.baseUrl || ''}
                      onChange={(e) => handleAIModelConfigChange({ baseUrl: e.target.value })}
                      placeholder="http://localhost:11434/v1"
                      className="w-full p-3 bg-gray-800 border border-gray-700 rounded focus:border-blue-500 focus:outline-none"
                    />
                  </div>
                )}

                {showApiKeySetup === config.aiModel.provider && (
                  <div className="p-4 bg-gray-900 rounded-lg">
                    <h4 className="font-medium mb-2">Setup Instructions:</h4>
                    <ol className="space-y-1 text-sm text-gray-300">
                      {getProviderSetupInstructions(config.aiModel.provider).map((instruction, index) => (
                        <li key={index}>{instruction}</li>
                      ))}
                    </ol>
                  </div>
                )}

                <div className="flex items-center gap-3">
                  <button
                    onClick={() => testConnection('ai-model')}
                    disabled={testResults['ai-model'] === 'testing' || !config.aiModel.apiKey}
                    className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 rounded transition-colors"
                  >
                    <TestTube className="w-4 h-4" />
                    {testResults['ai-model'] === 'testing' ? 'Testing...' : 'Test Connection'}
                  </button>
                  
                  {testResults['ai-model'] === 'success' && (
                    <div className="flex items-center gap-2 text-green-400">
                      <CheckCircle className="w-4 h-4" />
                      <span className="text-sm">Connection successful</span>
                    </div>
                  )}
                  
                  {testResults['ai-model'] === 'error' && (
                    <div className="flex items-center gap-2 text-red-400">
                      <XCircle className="w-4 h-4" />
                      <span className="text-sm">Connection failed</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'preferences' && (
            <div className="space-y-6">
              <div>
                <h2 className="text-lg font-semibold mb-4">Preferences</h2>
                <p className="text-gray-400 mb-6">
                  Customize the platform behavior and default settings.
                </p>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Theme</label>
                  <select
                    value={config.preferences.theme}
                    onChange={(e) => updatePreferences({ theme: e.target.value as 'light' | 'dark' | 'auto' })}
                    className="w-full p-3 bg-gray-800 border border-gray-700 rounded focus:border-blue-500 focus:outline-none"
                  >
                    <option value="dark">Dark</option>
                    <option value="light">Light</option>
                    <option value="auto">Auto (System)</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Default Framework</label>
                  <select
                    value={config.preferences.defaultFramework}
                    onChange={(e) => updatePreferences({ defaultFramework: e.target.value as 'react' | 'nextjs' | 'vue' | 'svelte' })}
                    className="w-full p-3 bg-gray-800 border border-gray-700 rounded focus:border-blue-500 focus:outline-none"
                  >
                    <option value="nextjs">Next.js</option>
                    <option value="react">React</option>
                    <option value="vue">Vue</option>
                    <option value="svelte">Svelte</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Default Styling</label>
                  <select
                    value={config.preferences.defaultStyling}
                    onChange={(e) => updatePreferences({ defaultStyling: e.target.value as 'tailwind' | 'css' | 'styled-components' })}
                    className="w-full p-3 bg-gray-800 border border-gray-700 rounded focus:border-blue-500 focus:outline-none"
                  >
                    <option value="tailwind">Tailwind CSS</option>
                    <option value="css">CSS</option>
                    <option value="styled-components">Styled Components</option>
                  </select>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <input
                      type="checkbox"
                      id="auto-save"
                      checked={config.preferences.autoSave}
                      onChange={(e) => updatePreferences({ autoSave: e.target.checked })}
                      className="text-blue-600"
                    />
                    <label htmlFor="auto-save" className="text-sm">
                      Auto-save generated projects
                    </label>
                  </div>

                  <div className="flex items-center gap-3">
                    <input
                      type="checkbox"
                      id="show-workflow-details"
                      checked={config.preferences.showWorkflowDetails}
                      onChange={(e) => updatePreferences({ showWorkflowDetails: e.target.checked })}
                      className="text-blue-600"
                    />
                    <label htmlFor="show-workflow-details" className="text-sm">
                      Show detailed workflow information
                    </label>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
